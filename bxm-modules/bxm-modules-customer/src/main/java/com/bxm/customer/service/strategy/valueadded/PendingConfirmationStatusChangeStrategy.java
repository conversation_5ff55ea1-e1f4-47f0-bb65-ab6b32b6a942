package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已交付待确认状态变更策略
 *
 * 处理从"已交付待确认"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. PENDING_CONFIRMATION -> CONFIRMED_PENDING_DEDUCTION (会计确认正常)
 * 2. PENDING_CONFIRMATION -> SUBMITTED_PENDING_DELIVERY (退回重新交付)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class PendingConfirmationStatusChangeStrategyValueAdded extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            // 验证会计确认
            validateAccountingConfirmation(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            // 验证退回重新交付
            validateReturnToDelivery(order, request);
        } else {
            throwUnsupportedTransition("已交付待确认", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    /**
     * 验证会计确认
     */
    private void validateAccountingConfirmation(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateContactInfo(order);

        // 验证账务信息是否完整
        if (order.getAccountingInfo() == null) {
            throw new IllegalArgumentException("账务类型信息不能为空");
        }

        // 验证账期信息
        if (order.getAccountingPeriodStart() == null || order.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("账期开始和结束时间不能为空");
        }

        // 验证账期逻辑
        if (order.getAccountingPeriodStart() > order.getAccountingPeriodEnd()) {
            throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
        }

        logValidationPassed("Accounting confirmation", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回重新交付
     */
    private void validateReturnToDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateReturnOperation(request, "重新交付");
        logValidationPassed("Return to delivery", request.getDeliveryOrderNo());
    }
}
