package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 交付异常状态变更策略
 *
 * 处理从"交付异常(待确认)"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. DELIVERY_EXCEPTION -> DELIVERY_CLOSED (关闭交付)
 * 2. DELIVERY_EXCEPTION -> SUBMITTED_PENDING_DELIVERY (重新交付)
 * 3. DELIVERY_EXCEPTION -> PENDING_CONFIRMATION (异常处理完成，直接进入待确认)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeliveryExceptionStatusChangeStrategyValueAdded extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED ||
               targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
               targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED) {
            // 验证关闭交付
            validateCloseDelivery(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            // 验证重新交付
            validateRetryDelivery(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            // 验证异常处理完成
            validateExceptionResolved(order, request);
        } else {
            throwUnsupportedTransition("交付异常", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION;
    }

    /**
     * 验证关闭交付
     *
     * 适用场景：
     * 1. 客户取消交付需求
     * 2. 交付条件无法满足
     * 3. 业务变更，不再需要交付
     */
    private void validateCloseDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateCloseOperation(request, "交付");
        logValidationPassed("Close delivery", request.getDeliveryOrderNo());
    }

    /**
     * 验证重新交付
     *
     * 适用场景：
     * 1. 异常问题已解决，可以重新交付
     * 2. 交付条件已满足
     * 3. 客户要求重新交付
     */
    private void validateRetryDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateReason(request.getReason(), 5, "重新交付");

        // 验证交付相关信息是否完整
        validateNotEmpty(order.getRequirements(), "交付要求");

        if (order.getDdl() == null) {
            throw new IllegalArgumentException("交付截止日期不能为空");
        }

        if (order.getBusinessDeptId() == null) {
            throw new IllegalArgumentException("业务部门ID不能为空");
        }

        logValidationPassed("Retry delivery", request.getDeliveryOrderNo());
    }

    /**
     * 验证异常处理完成
     *
     * 适用场景：
     * 1. 异常已通过其他方式解决（如线下交付）
     * 2. 交付已通过其他渠道完成
     * 3. 客户确认可以直接进入确认阶段
     */
    private void validateExceptionResolved(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateReason(request.getReason(), 10, "异常解决");
        validateRemark(request.getRemark(), "异常解决");

        // 验证交付相关信息完整性（确保可以进入待确认状态）
        validateNotEmpty(order.getRequirements(), "交付要求");

        logValidationPassed("Exception resolution", request.getDeliveryOrderNo());
    }
}
